package com.xapkinstaller.parser

import android.content.Context
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.xapkinstaller.model.InstallResult
import com.xapkinstaller.model.XAPKInfo
import org.zeroturnaround.zip.ZipUtil
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

/**
 * XAPK file parser and extractor
 */
class XAPKParser(private val context: Context) {
    
    private val gson = Gson()
    
    /**
     * Validate if file is a valid XAPK
     */
    fun isValidXAPK(xapkFile: File): Boolean {
        if (!xapkFile.exists() || !xapkFile.canRead()) {
            return false
        }
        
        // Check file extension
        if (!xapkFile.name.lowercase().endsWith(".xapk")) {
            return false
        }
        
        // Check if it's a valid ZIP file and contains manifest.json
        return try {
            ZipInputStream(FileInputStream(xapkFile)).use { zipStream ->
                var hasManifest = false
                var entry: ZipEntry?
                
                while (zipStream.nextEntry.also { entry = it } != null) {
                    if (entry?.name == "manifest.json") {
                        hasManifest = true
                        break
                    }
                }
                hasManifest
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Extract XAPK file to temporary directory
     */
    fun extractXAPK(xapkFile: File): File? {
        return try {
            val extractDir = File(context.cacheDir, "xapk_extract_${System.currentTimeMillis()}")
            if (!extractDir.exists()) {
                extractDir.mkdirs()
            }
            
            ZipUtil.unpack(xapkFile, extractDir)
            extractDir
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Parse manifest.json from extracted XAPK directory
     */
    fun parseManifest(extractedDir: File): XAPKInfo? {
        val manifestFile = File(extractedDir, "manifest.json")
        if (!manifestFile.exists()) {
            return null
        }
        
        return try {
            val manifestContent = manifestFile.readText()
            gson.fromJson(manifestContent, XAPKInfo::class.java)
        } catch (e: JsonSyntaxException) {
            e.printStackTrace()
            null
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Get all APK files from extracted directory
     */
    fun getApkFiles(extractedDir: File, xapkInfo: XAPKInfo): List<File> {
        val apkFiles = mutableListOf<File>()
        
        // Add main APK (usually named after package)
        val mainApk = File(extractedDir, "${xapkInfo.packageName}.apk")
        if (mainApk.exists()) {
            apkFiles.add(mainApk)
        }
        
        // Add split APKs
        xapkInfo.splitApks?.forEach { splitApk ->
            val apkFile = File(extractedDir, splitApk.file)
            if (apkFile.exists()) {
                apkFiles.add(apkFile)
            }
        }
        
        // If no APKs found using manifest, scan directory for .apk files
        if (apkFiles.isEmpty()) {
            extractedDir.listFiles { _, name -> 
                name.lowercase().endsWith(".apk") 
            }?.let { apkFiles.addAll(it) }
        }
        
        return apkFiles
    }
    
    /**
     * Get all OBB files from extracted directory
     */
    fun getObbFiles(extractedDir: File, xapkInfo: XAPKInfo): List<File> {
        val obbFiles = mutableListOf<File>()
        
        // Add expansion files (OBBs)
        xapkInfo.expansions?.forEach { expansion ->
            val obbFile = File(extractedDir, expansion.file)
            if (obbFile.exists()) {
                obbFiles.add(obbFile)
            }
        }
        
        // If no OBBs found using manifest, scan directory for .obb files
        if (obbFiles.isEmpty()) {
            extractedDir.listFiles { _, name -> 
                name.lowercase().endsWith(".obb") 
            }?.let { obbFiles.addAll(it) }
        }
        
        return obbFiles
    }
    
    /**
     * Clean up extracted directory
     */
    fun cleanup(extractedDir: File) {
        try {
            if (extractedDir.exists()) {
                extractedDir.deleteRecursively()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
