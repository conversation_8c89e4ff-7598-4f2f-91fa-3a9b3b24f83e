<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- File Selection -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="File Selection"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/btnSelectFile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/select_xapk_file"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvSelectedFile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/no_file_selected"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Device Info -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/device_info"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvDeviceInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvCompatibilityWarning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@android:color/holo_orange_dark"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Permissions -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/permissions_status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvPermissionStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/btnGrantPermissions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/grant_permissions"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Installation -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Installation"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/btnInstall"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/install_xapk"
                    android:enabled="false"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/installation_progress"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:max="100"
                    android:progress="0"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Ready to install"
                    android:textSize="14sp"
                    android:gravity="center" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Log -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Installation Log"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:background="@android:color/black">

                    <TextView
                        android:id="@+id/tvLog"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ready to install XAPK files...\n"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:fontFamily="monospace"
                        android:padding="8dp" />

                </ScrollView>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
