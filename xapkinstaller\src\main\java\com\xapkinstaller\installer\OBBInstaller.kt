package com.xapkinstaller.installer

import android.content.Context
import android.os.Environment
import com.xapkinstaller.model.InstallResult
import com.xapkinstaller.model.XAPKInfo
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

/**
 * OBB file installer utility class
 */
class OBBInstaller(private val context: Context) {
    
    companion object {
        private const val OBB_DIRECTORY = "Android/obb"
    }
    
    /**
     * Install OBB files to the correct directory
     */
    fun installOBBFiles(obbFiles: List<File>, xapkInfo: XAPKInfo): InstallResult {
        if (obbFiles.isEmpty()) {
            return InstallResult.success(message = "No OBB files to install")
        }
        
        try {
            val obbDir = getOBBDirectory(xapkInfo.packageName)
            if (!obbDir.exists() && !obbDir.mkdirs()) {
                return InstallResult.error(
                    InstallResult.ERROR_OBB_COPY_FAILED,
                    "Failed to create OBB directory: ${obbDir.absolutePath}"
                )
            }
            
            obbFiles.forEach { obbFile ->
                val targetFile = File(obbDir, obbFile.name)
                val copyResult = copyFile(obbFile, targetFile)
                if (!copyResult) {
                    return InstallResult.error(
                        InstallResult.ERROR_OBB_COPY_FAILED,
                        "Failed to copy OBB file: ${obbFile.name}"
                    )
                }
            }
            
            return InstallResult.success(
                packageName = xapkInfo.packageName,
                message = "OBB files installed successfully"
            )
            
        } catch (e: Exception) {
            return InstallResult.error(
                InstallResult.ERROR_OBB_COPY_FAILED,
                "Error installing OBB files: ${e.message}"
            )
        }
    }
    
    /**
     * Get the OBB directory for a specific package
     */
    private fun getOBBDirectory(packageName: String): File {
        // Try external storage first
        val externalStorage = Environment.getExternalStorageDirectory()
        val externalObbDir = File(externalStorage, "$OBB_DIRECTORY/$packageName")
        
        if (externalStorage.canWrite()) {
            return externalObbDir
        }
        
        // Fallback to app-specific external directory
        val appExternalDir = context.getExternalFilesDir(null)?.parentFile
        return if (appExternalDir != null) {
            File(appExternalDir, "obb/$packageName")
        } else {
            // Last resort: use internal cache
            File(context.cacheDir, "obb/$packageName")
        }
    }
    
    /**
     * Copy file from source to destination
     */
    private fun copyFile(source: File, destination: File): Boolean {
        return try {
            // Create parent directories if they don't exist
            destination.parentFile?.mkdirs()
            
            FileInputStream(source).use { input ->
                FileOutputStream(destination).use { output ->
                    input.copyTo(output)
                }
            }
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Check if OBB files already exist for a package
     */
    fun hasExistingOBBFiles(packageName: String): Boolean {
        val obbDir = getOBBDirectory(packageName)
        return obbDir.exists() && obbDir.listFiles()?.isNotEmpty() == true
    }
    
    /**
     * Remove existing OBB files for a package
     */
    fun removeExistingOBBFiles(packageName: String): Boolean {
        return try {
            val obbDir = getOBBDirectory(packageName)
            if (obbDir.exists()) {
                obbDir.deleteRecursively()
            } else {
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Get the size of OBB files for a package
     */
    fun getOBBFilesSize(packageName: String): Long {
        val obbDir = getOBBDirectory(packageName)
        return if (obbDir.exists()) {
            obbDir.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } else {
            0L
        }
    }
    
    /**
     * Check if there's enough space for OBB files
     */
    fun hasEnoughSpaceForOBB(obbFiles: List<File>): Boolean {
        val requiredSpace = obbFiles.sumOf { it.length() }
        val availableSpace = getAvailableSpace()
        return availableSpace > requiredSpace
    }
    
    /**
     * Get available storage space
     */
    private fun getAvailableSpace(): Long {
        return try {
            val externalStorage = Environment.getExternalStorageDirectory()
            externalStorage.freeSpace
        } catch (e: Exception) {
            // Fallback to internal storage
            context.cacheDir.freeSpace
        }
    }
}
