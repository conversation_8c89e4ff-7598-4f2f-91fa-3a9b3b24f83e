apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.xapkinstaller.demo"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation project(':xapkinstaller')
    
    implementation "androidx.core:core-ktx:$rootProject.ext.core_ktx_version"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appcompat_version"
    implementation "com.google.android.material:material:$rootProject.ext.material_version"
    implementation "androidx.constraintlayout:constraintlayout:$rootProject.ext.constraintlayout_version"
    
    // For file picker
    implementation 'androidx.activity:activity-ktx:1.6.1'
    implementation 'androidx.fragment:fragment-ktx:1.5.5'
    
    // For coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
