package com.xapkinstaller.model

/**
 * Installation result data class
 */
data class InstallResult(
    val success: <PERSON>olean,
    val message: String,
    val errorCode: Int = 0,
    val packageName: String? = null
) {
    companion object {
        // Error codes
        const val ERROR_INVALID_XAPK = 1001
        const val ERROR_EXTRACTION_FAILED = 1002
        const val ERROR_MANIFEST_PARSE_FAILED = 1003
        const val ERROR_APK_INSTALL_FAILED = 1004
        const val ERROR_OBB_COPY_FAILED = 1005
        const val ERROR_PERMISSION_DENIED = 1006
        const val ERROR_INSUFFICIENT_STORAGE = 1007
        const val ERROR_UNSUPPORTED_DEVICE = 1008
        const val ERROR_UNKNOWN = 1999
        
        fun success(packageName: String? = null, message: String = "Installation completed successfully") = 
            InstallResult(true, message, 0, packageName)
            
        fun error(errorCode: Int, message: String, packageName: String? = null) = 
            InstallResult(false, message, errorCode, packageName)
    }
}

/**
 * Installation progress data class
 */
data class InstallProgress(
    val stage: InstallStage,
    val progress: Int, // 0-100
    val message: String
)

/**
 * Installation stages
 */
enum class InstallStage {
    VALIDATING,
    EXTRACTING,
    PARSING_MANIFEST,
    INSTALLING_APKS,
    COPYING_OBBS,
    COMPLETED,
    FAILED
}
