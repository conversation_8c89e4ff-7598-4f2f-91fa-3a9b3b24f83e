# XAPK Installer SDK

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![API](https://img.shields.io/badge/API-21%2B-brightgreen.svg?style=flat)](https://android-arsenal.com/api?level=21)

A powerful Android SDK for installing XAPK files with automatic APK and OBB handling. This library provides a simple and robust solution for installing XAPK packages that contain single or multiple APK files along with OBB expansion files.

## Features

- ✅ **XAPK File Recognition**: Automatically detects and validates XAPK file format
- ✅ **APK Installation**: Supports both single APK and split APK (multiple APK) installation
- ✅ **OBB File Handling**: Automatically extracts and places OBB files in the correct directory
- ✅ **Permission Management**: Handles all required permissions for installation
- ✅ **Device Compatibility**: Checks device compatibility and provides warnings for problematic ROMs
- ✅ **Progress Tracking**: Real-time installation progress with detailed callbacks
- ✅ **Error Handling**: Comprehensive error reporting with specific error codes
- ✅ **Modern Android Support**: Compatible with Android 5.0+ (API 21+)
- ✅ **Kotlin First**: Written in Kotlin with full Java interoperability

## Installation

### Gradle

Add it in your root `build.gradle` at the end of repositories:

```gradle
allprojects {
    repositories {
        ...
        maven { url 'https://jitpack.io' }
    }
}
```

Add the dependency in your app's `build.gradle`:

```gradle
dependencies {
    implementation 'com.github.yourusername:xapkinstaller-sdk:1.0.0'
}
```

### Permissions

Add the following permissions to your `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
```

## Quick Start

### Basic Usage

```kotlin
val installer = XAPKInstaller(context)

// Check if XAPK file is valid
if (installer.isValidXAPK(xapkFile)) {
    // Install XAPK with progress tracking
    installer.install(xapkFile, object : InstallListener {
        override fun onInstallStarted(packageName: String) {
            // Installation started
            Log.d("XAPK", "Installing: $packageName")
        }
        
        override fun onProgressUpdate(progress: InstallProgress) {
            // Update progress UI
            progressBar.progress = progress.progress
            statusText.text = progress.message
        }
        
        override fun onInstallCompleted(result: InstallResult) {
            if (result.success) {
                Toast.makeText(context, "Installation completed!", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "Installation failed: ${result.message}", Toast.LENGTH_LONG).show()
            }
        }
    })
}
```

### Permission Handling

```kotlin
// Check if all required permissions are granted
if (!installer.hasRequiredPermissions()) {
    // Request permissions
    val permissionStatus = installer.getPermissionStatus()
    Log.d("XAPK", "Permission status: $permissionStatus")
    
    // Guide user to grant permissions
    // (See demo app for complete implementation)
}
```

### Device Compatibility

```kotlin
// Check device compatibility
if (!installer.isDeviceCompatible()) {
    Log.w("XAPK", "Device may not be compatible")
}

// Get device information
val deviceInfo = installer.getDeviceInfo()
Log.d("XAPK", "Device: $deviceInfo")

// Check for ROM-specific warnings
val warning = installer.getCompatibilityWarning()
if (warning != null) {
    Log.w("XAPK", "Compatibility warning: $warning")
}
```

## API Reference

### XAPKInstaller

Main class for XAPK installation operations.

#### Methods

| Method | Description |
|--------|-------------|
| `install(File, InstallListener)` | Install XAPK file with progress tracking |
| `isValidXAPK(File): Boolean` | Check if file is a valid XAPK |
| `hasRequiredPermissions(): Boolean` | Check if all required permissions are granted |
| `getPermissionStatus(): String` | Get human-readable permission status |
| `isDeviceCompatible(): Boolean` | Check device compatibility |
| `getDeviceInfo(): String` | Get device information |
| `getCompatibilityWarning(): String?` | Get compatibility warning if any |

### InstallListener

Interface for receiving installation progress and results.

#### Methods

| Method | Description |
|--------|-------------|
| `onInstallStarted(String)` | Called when installation starts |
| `onProgressUpdate(InstallProgress)` | Called when progress updates |
| `onInstallCompleted(InstallResult)` | Called when installation completes |

### InstallResult

Data class containing installation result information.

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `success` | Boolean | Whether installation was successful |
| `message` | String | Result message |
| `errorCode` | Int | Error code (if failed) |
| `packageName` | String? | Package name of installed app |

#### Error Codes

| Code | Description |
|------|-------------|
| `ERROR_INVALID_XAPK` | Invalid XAPK file |
| `ERROR_EXTRACTION_FAILED` | Failed to extract XAPK |
| `ERROR_MANIFEST_PARSE_FAILED` | Failed to parse manifest.json |
| `ERROR_APK_INSTALL_FAILED` | APK installation failed |
| `ERROR_OBB_COPY_FAILED` | OBB file copy failed |
| `ERROR_PERMISSION_DENIED` | Required permissions not granted |
| `ERROR_INSUFFICIENT_STORAGE` | Not enough storage space |
| `ERROR_UNSUPPORTED_DEVICE` | Device not supported |

## Device Compatibility

### Supported Android Versions

- **Minimum**: Android 5.0 (API 21)
- **Target**: Android 13 (API 33)
- **Recommended**: Android 6.0+ (API 23) for best experience

### Known Issues

#### MIUI (Xiaomi)
- **Issue**: Split APK installation may fail
- **Solution**: Disable MIUI optimization in Developer Options

#### Flyme OS (Meizu)
- **Issue**: System modifications prevent split APK installation
- **Solution**: Use single APK XAPK files when possible

#### Funtouch OS (Vivo)
- **Issue**: Custom installation restrictions
- **Solution**: May require manual APK installation

## Sample App

The repository includes a complete sample application demonstrating all SDK features:

- File selection and validation
- Permission management
- Device compatibility checking
- Installation progress tracking
- Error handling
- Logging and debugging

To run the sample app:

1. Clone the repository
2. Open in Android Studio
3. Build and run the `app` module

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

```
Copyright 2024 XAPK Installer SDK

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
```

## Acknowledgments

- [zt-zip](https://github.com/zeroturnaround/zt-zip) for ZIP file handling
- [Gson](https://github.com/google/gson) for JSON parsing
- Inspired by [XAPKInstaller](https://github.com/andywu91/XAPKInstaller) by andywu91
