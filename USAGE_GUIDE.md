# XAPK Installer SDK - 使用指南

## 概述

XAPK Installer SDK 是一个强大的Android库，用于自动识别和安装XAPK文件。它支持单个APK和多个APK（split APK）的安装，同时自动处理OBB扩展文件。

## 快速开始

### 1. 添加依赖

在项目根目录的 `build.gradle` 中添加：

```gradle
allprojects {
    repositories {
        ...
        maven { url 'https://jitpack.io' }
    }
}
```

在应用模块的 `build.gradle` 中添加：

```gradle
dependencies {
    implementation 'com.github.yourusername:xapkinstaller-sdk:1.0.0'
}
```

### 2. 添加权限

在 `AndroidManifest.xml` 中添加必要权限：

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
```

### 3. 基本使用

```kotlin
// 创建安装器实例
val installer = XAPKInstaller(context)

// 检查XAPK文件是否有效
if (installer.isValidXAPK(xapkFile)) {
    // 开始安装
    installer.install(xapkFile, object : InstallListener {
        override fun onInstallStarted(packageName: String) {
            Log.d("XAPK", "开始安装: $packageName")
        }
        
        override fun onProgressUpdate(progress: InstallProgress) {
            // 更新进度条
            progressBar.progress = progress.progress
            statusText.text = progress.message
        }
        
        override fun onInstallCompleted(result: InstallResult) {
            if (result.success) {
                Toast.makeText(context, "安装成功！", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "安装失败: ${result.message}", Toast.LENGTH_LONG).show()
            }
        }
    })
}
```

## 详细功能

### 权限管理

```kotlin
// 检查是否有所有必需权限
if (!installer.hasRequiredPermissions()) {
    // 获取权限状态信息
    val status = installer.getPermissionStatus()
    Log.d("XAPK", "权限状态: $status")
    
    // 引导用户授予权限
    // Android 11+ 需要特殊处理 MANAGE_EXTERNAL_STORAGE
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
        intent.data = Uri.parse("package:${context.packageName}")
        startActivity(intent)
    }
}
```

### 设备兼容性检查

```kotlin
// 检查设备兼容性
if (!installer.isDeviceCompatible()) {
    Log.w("XAPK", "设备可能不兼容")
}

// 获取设备信息
val deviceInfo = installer.getDeviceInfo()
Log.d("XAPK", "设备信息: $deviceInfo")

// 检查ROM特定警告
val warning = installer.getCompatibilityWarning()
if (warning != null) {
    Log.w("XAPK", "兼容性警告: $warning")
    // 显示警告给用户
}
```

### 错误处理

```kotlin
override fun onInstallCompleted(result: InstallResult) {
    if (!result.success) {
        when (result.errorCode) {
            InstallResult.ERROR_INVALID_XAPK -> {
                // 无效的XAPK文件
                showError("文件格式无效")
            }
            InstallResult.ERROR_PERMISSION_DENIED -> {
                // 权限被拒绝
                showError("需要授予安装权限")
            }
            InstallResult.ERROR_INSUFFICIENT_STORAGE -> {
                // 存储空间不足
                showError("存储空间不足")
            }
            InstallResult.ERROR_UNSUPPORTED_DEVICE -> {
                // 设备不支持
                showError("设备不支持此应用")
            }
            else -> {
                showError("安装失败: ${result.message}")
            }
        }
    }
}
```

## 高级用法

### 自定义安装监听器

```kotlin
class CustomInstallListener : InstallListener {
    override fun onInstallStarted(packageName: String) {
        // 记录安装开始时间
        val startTime = System.currentTimeMillis()
        // 显示安装对话框
        showInstallDialog(packageName)
    }
    
    override fun onProgressUpdate(progress: InstallProgress) {
        // 根据不同阶段显示不同信息
        when (progress.stage) {
            InstallStage.VALIDATING -> updateStatus("验证文件中...")
            InstallStage.EXTRACTING -> updateStatus("解压文件中...")
            InstallStage.INSTALLING_APKS -> updateStatus("安装APK中...")
            InstallStage.COPYING_OBBS -> updateStatus("复制OBB文件中...")
            InstallStage.COMPLETED -> updateStatus("安装完成")
            InstallStage.FAILED -> updateStatus("安装失败")
        }
        
        // 更新进度条
        updateProgress(progress.progress)
    }
    
    override fun onInstallCompleted(result: InstallResult) {
        dismissInstallDialog()
        
        if (result.success) {
            // 记录成功安装
            logInstallSuccess(result.packageName)
            // 可选：启动已安装的应用
            launchInstalledApp(result.packageName)
        } else {
            // 记录安装失败
            logInstallFailure(result.errorCode, result.message)
        }
    }
}
```

### 批量安装

```kotlin
class BatchInstaller(private val context: Context) {
    private val installer = XAPKInstaller(context)
    private val installQueue = mutableListOf<File>()
    private var currentIndex = 0
    
    fun addToQueue(xapkFile: File) {
        if (installer.isValidXAPK(xapkFile)) {
            installQueue.add(xapkFile)
        }
    }
    
    fun startBatchInstall(listener: BatchInstallListener) {
        if (installQueue.isEmpty()) {
            listener.onBatchCompleted(emptyList())
            return
        }
        
        installNext(listener)
    }
    
    private fun installNext(listener: BatchInstallListener) {
        if (currentIndex >= installQueue.size) {
            listener.onBatchCompleted(installQueue)
            return
        }
        
        val currentFile = installQueue[currentIndex]
        installer.install(currentFile, object : InstallListener {
            override fun onInstallStarted(packageName: String) {
                listener.onFileInstallStarted(currentIndex, packageName)
            }
            
            override fun onProgressUpdate(progress: InstallProgress) {
                listener.onFileProgressUpdate(currentIndex, progress)
            }
            
            override fun onInstallCompleted(result: InstallResult) {
                listener.onFileInstallCompleted(currentIndex, result)
                currentIndex++
                installNext(listener)
            }
        })
    }
}
```

## 最佳实践

### 1. 权限请求时机
- 在用户选择XAPK文件后再请求权限
- 提供清晰的权限说明
- 处理权限被拒绝的情况

### 2. 用户体验
- 显示详细的安装进度
- 提供取消安装的选项
- 在安装完成后提供启动应用的选项

### 3. 错误处理
- 为每种错误类型提供具体的解决方案
- 记录详细的错误日志用于调试
- 提供重试机制

### 4. 性能优化
- 在后台线程进行文件操作
- 及时清理临时文件
- 监控内存使用情况

## 常见问题

### Q: 在MIUI系统上安装失败怎么办？
A: 需要在开发者选项中关闭"MIUI优化"。SDK会自动检测MIUI系统并提供相应警告。

### Q: Android 11+上权限请求失败？
A: Android 11+需要特殊处理MANAGE_EXTERNAL_STORAGE权限，需要跳转到系统设置页面。

### Q: 如何处理大型OBB文件？
A: SDK会自动检查存储空间，确保有足够空间后再进行复制操作。

### Q: 支持哪些XAPK格式？
A: 支持标准XAPK格式，包含manifest.json、APK文件和OBB文件。

## 示例项目

项目中包含完整的示例应用，展示了所有功能的使用方法：

- 文件选择和验证
- 权限管理
- 设备兼容性检查
- 安装进度跟踪
- 错误处理
- 日志记录

运行示例应用：
1. 克隆项目
2. 在Android Studio中打开
3. 运行`app`模块

## 技术支持

如有问题或建议，请提交Issue或Pull Request。
