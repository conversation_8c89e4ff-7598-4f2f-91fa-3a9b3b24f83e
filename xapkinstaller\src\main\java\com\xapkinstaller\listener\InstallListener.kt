package com.xapkinstaller.listener

import com.xapkinstaller.model.InstallProgress
import com.xapkinstaller.model.InstallResult

/**
 * Installation progress and result listener
 */
interface InstallListener {
    /**
     * Called when installation starts
     */
    fun onInstallStarted(packageName: String)
    
    /**
     * Called when installation progress updates
     * @param progress Installation progress information
     */
    fun onProgressUpdate(progress: InstallProgress)
    
    /**
     * Called when installation completes (success or failure)
     * @param result Installation result
     */
    fun onInstallCompleted(result: InstallResult)
}

/**
 * Simple adapter class for InstallListener
 */
abstract class SimpleInstallListener : InstallListener {
    override fun onInstallStarted(packageName: String) {}
    override fun onProgressUpdate(progress: InstallProgress) {}
}
