package com.xapkinstaller.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.core.content.ContextCompat

/**
 * Permission utility class for XAPK installation
 */
object PermissionUtils {
    
    /**
     * Check if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): <PERSON><PERSON><PERSON> {
        return hasStoragePermission(context) && hasInstallPermission(context)
    }
    
    /**
     * Check storage permission
     */
    fun hasStoragePermission(context: Context): <PERSON><PERSON><PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - Check for MANAGE_EXTERNAL_STORAGE
            Environment.isExternalStorageManager()
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0+ - Check for READ/WRITE_EXTERNAL_STORAGE
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Below Android 6.0 - permissions are granted at install time
            true
        }
    }
    
    /**
     * Check install permission
     */
    fun hasInstallPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0+ - Check for REQUEST_INSTALL_PACKAGES
            context.packageManager.canRequestPackageInstalls()
        } else {
            // Below Android 8.0 - check for INSTALL_PACKAGES (system permission)
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.INSTALL_PACKAGES
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Get required permissions array for requesting
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - MANAGE_EXTERNAL_STORAGE is handled separately
            emptyArray()
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        } else {
            emptyArray()
        }
    }
    
    /**
     * Create intent to request storage permission (Android 11+)
     */
    fun createStoragePermissionIntent(): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                data = Uri.parse("package:${getPackageName()}")
            }
        } else {
            null
        }
    }
    
    /**
     * Create intent to request install permission (Android 8.0+)
     */
    fun createInstallPermissionIntent(context: Context): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                data = Uri.parse("package:${context.packageName}")
            }
        } else {
            null
        }
    }
    
    /**
     * Get permission status message
     */
    fun getPermissionStatusMessage(context: Context): String {
        val storageOk = hasStoragePermission(context)
        val installOk = hasInstallPermission(context)
        
        return when {
            storageOk && installOk -> "All permissions granted"
            !storageOk && !installOk -> "Storage and install permissions required"
            !storageOk -> "Storage permission required"
            !installOk -> "Install permission required"
            else -> "Unknown permission status"
        }
    }
    
    private fun getPackageName(): String {
        // This should be replaced with actual package name in implementation
        return "com.xapkinstaller"
    }
}
