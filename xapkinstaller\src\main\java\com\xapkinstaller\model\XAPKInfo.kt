package com.xapkinstaller.model

import com.google.gson.annotations.SerializedName

/**
 * XAPK manifest.json data model
 */
data class XAPKInfo(
    @SerializedName("xapk_version")
    val xapkVersion: Int = 1,
    
    @SerializedName("package_name")
    val packageName: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("version_code")
    val versionCode: Long,
    
    @SerializedName("version_name")
    val versionName: String,
    
    @SerializedName("min_sdk_version")
    val minSdkVersion: Int? = null,
    
    @SerializedName("target_sdk_version")
    val targetSdkVersion: Int? = null,
    
    @SerializedName("permissions")
    val permissions: List<String>? = null,
    
    @SerializedName("split_apks")
    val splitApks: List<SplitApk>? = null,
    
    @SerializedName("expansions")
    val expansions: List<ExpansionFile>? = null,
    
    @SerializedName("icon")
    val icon: String? = null,
    
    @SerializedName("total_size")
    val totalSize: Long? = null
)

/**
 * Split APK information
 */
data class SplitApk(
    @SerializedName("file")
    val file: String,
    
    @SerializedName("id")
    val id: String? = null,
    
    @SerializedName("install_location")
    val installLocation: String? = null
)

/**
 * OBB/Expansion file information
 */
data class ExpansionFile(
    @SerializedName("file")
    val file: String,
    
    @SerializedName("install_location")
    val installLocation: String,
    
    @SerializedName("install_path")
    val installPath: String? = null
)
