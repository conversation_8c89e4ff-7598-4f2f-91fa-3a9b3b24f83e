package com.xapkinstaller.demo

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.xapkinstaller.XAPKInstaller
import com.xapkinstaller.demo.databinding.ActivityMainBinding
import com.xapkinstaller.listener.InstallListener
import com.xapkinstaller.model.InstallProgress
import com.xapkinstaller.model.InstallResult
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : AppCompatActivity(), InstallListener {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var xapkInstaller: XAPKInstaller
    private var selectedXapkFile: File? = null
    
    private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    
    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { handleSelectedFile(it) }
    }
    
    // Permission request launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        updatePermissionStatus()
        updateInstallButtonState()
    }
    
    // Settings launcher for special permissions
    private val settingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        updatePermissionStatus()
        updateInstallButtonState()
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        xapkInstaller = XAPKInstaller(this)
        
        setupUI()
        updateDeviceInfo()
        updatePermissionStatus()
        
        // Handle intent if app was opened with XAPK file
        handleIntent(intent)
    }
    
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let { handleIntent(it) }
    }
    
    private fun setupUI() {
        binding.btnSelectFile.setOnClickListener {
            filePickerLauncher.launch("*/*")
        }
        
        binding.btnGrantPermissions.setOnClickListener {
            requestPermissions()
        }
        
        binding.btnInstall.setOnClickListener {
            selectedXapkFile?.let { file ->
                installXAPK(file)
            }
        }
    }
    
    private fun handleIntent(intent: Intent) {
        if (intent.action == Intent.ACTION_VIEW) {
            intent.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }
    
    private fun handleSelectedFile(uri: Uri) {
        try {
            // Copy file to cache directory for processing
            val inputStream = contentResolver.openInputStream(uri)
            val fileName = getFileName(uri) ?: "selected.xapk"
            val tempFile = File(cacheDir, fileName)
            
            inputStream?.use { input ->
                tempFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            
            selectedXapkFile = tempFile
            binding.tvSelectedFile.text = getString(R.string.file_selected, fileName)
            
            // Validate XAPK file
            if (xapkInstaller.isValidXAPK(tempFile)) {
                logMessage("✓ Valid XAPK file selected: $fileName")
                updateInstallButtonState()
            } else {
                logMessage("✗ Invalid XAPK file: $fileName")
                Toast.makeText(this, R.string.invalid_xapk, Toast.LENGTH_SHORT).show()
                selectedXapkFile = null
                binding.tvSelectedFile.text = getString(R.string.no_file_selected)
            }
            
        } catch (e: Exception) {
            logMessage("✗ Error selecting file: ${e.message}")
            Toast.makeText(this, "Error selecting file: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun getFileName(uri: Uri): String? {
        return contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
            cursor.moveToFirst()
            cursor.getString(nameIndex)
        }
    }
    
    private fun updateDeviceInfo() {
        binding.tvDeviceInfo.text = xapkInstaller.getDeviceInfo()
        
        val warning = xapkInstaller.getCompatibilityWarning()
        if (warning != null) {
            binding.tvCompatibilityWarning.text = warning
            binding.tvCompatibilityWarning.visibility = android.view.View.VISIBLE
        }
        
        if (!xapkInstaller.isDeviceCompatible()) {
            logMessage("⚠ Device compatibility issue detected")
        }
    }
    
    private fun updatePermissionStatus() {
        val status = xapkInstaller.getPermissionStatus()
        binding.tvPermissionStatus.text = status
        
        val hasPermissions = xapkInstaller.hasRequiredPermissions()
        binding.btnGrantPermissions.isEnabled = !hasPermissions
        
        if (hasPermissions) {
            logMessage("✓ All required permissions granted")
        } else {
            logMessage("⚠ Missing required permissions")
        }
    }
    
    private fun requestPermissions() {
        val permissions = mutableListOf<String>()
        
        // Storage permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - Request MANAGE_EXTERNAL_STORAGE
            if (!android.os.Environment.isExternalStorageManager()) {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:$packageName")
                settingsLauncher.launch(intent)
                return
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) 
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
        
        // Install permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!packageManager.canRequestPackageInstalls()) {
                val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                intent.data = Uri.parse("package:$packageName")
                settingsLauncher.launch(intent)
                return
            }
        }
        
        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        }
    }
    
    private fun updateInstallButtonState() {
        val hasFile = selectedXapkFile != null
        val hasPermissions = xapkInstaller.hasRequiredPermissions()
        val isCompatible = xapkInstaller.isDeviceCompatible()
        
        binding.btnInstall.isEnabled = hasFile && hasPermissions && isCompatible
    }
    
    private fun installXAPK(file: File) {
        if (!xapkInstaller.hasRequiredPermissions()) {
            Toast.makeText(this, R.string.permissions_required, Toast.LENGTH_SHORT).show()
            return
        }
        
        if (!xapkInstaller.isDeviceCompatible()) {
            Toast.makeText(this, R.string.device_incompatible, Toast.LENGTH_SHORT).show()
            return
        }
        
        logMessage("Starting XAPK installation...")
        binding.btnInstall.isEnabled = false
        binding.progressBar.progress = 0
        binding.tvProgress.text = "Preparing installation..."
        
        xapkInstaller.install(file, this)
    }
    
    private fun logMessage(message: String) {
        val timestamp = dateFormat.format(Date())
        val logEntry = "[$timestamp] $message\n"
        binding.tvLog.append(logEntry)
        
        // Auto-scroll to bottom
        binding.tvLog.post {
            val scrollView = binding.tvLog.parent as? android.widget.ScrollView
            scrollView?.fullScroll(android.view.View.FOCUS_DOWN)
        }
    }
    
    // InstallListener implementation
    override fun onInstallStarted(packageName: String) {
        runOnUiThread {
            logMessage("Installation started for: $packageName")
            Toast.makeText(this, getString(R.string.installation_started, packageName), Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onProgressUpdate(progress: InstallProgress) {
        runOnUiThread {
            binding.progressBar.progress = progress.progress
            binding.tvProgress.text = progress.message
            logMessage("${progress.stage}: ${progress.message} (${progress.progress}%)")
        }
    }
    
    override fun onInstallCompleted(result: InstallResult) {
        runOnUiThread {
            binding.btnInstall.isEnabled = true
            
            if (result.success) {
                binding.progressBar.progress = 100
                binding.tvProgress.text = "Installation completed successfully"
                logMessage("✓ Installation completed successfully")
                Toast.makeText(this, R.string.installation_completed, Toast.LENGTH_LONG).show()
            } else {
                binding.tvProgress.text = "Installation failed"
                logMessage("✗ Installation failed: ${result.message}")
                Toast.makeText(this, getString(R.string.installation_failed, result.message), Toast.LENGTH_LONG).show()
            }
        }
    }
}
