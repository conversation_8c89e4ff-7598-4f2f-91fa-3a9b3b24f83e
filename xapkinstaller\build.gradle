apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'maven-publish'

group = 'com.xapkinstaller'
version = '1.0.0'

android {
    compileSdk 33

    defaultConfig {
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation "androidx.core:core-ktx:$rootProject.ext.core_ktx_version"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appcompat_version"
    
    // For ZIP file handling
    implementation 'org.zeroturnaround:zt-zip:1.15'
    
    // For JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // For coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

// Publishing configuration
afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release
                artifactId = 'xapkinstaller'
                
                pom {
                    name = 'XAPK Installer SDK'
                    description = 'Android SDK for installing XAPK files with automatic APK and OBB handling'
                    url = 'https://github.com/your-username/xapkinstaller-sdk'
                    
                    licenses {
                        license {
                            name = 'The Apache License, Version 2.0'
                            url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                        }
                    }
                }
            }
        }
    }
}
