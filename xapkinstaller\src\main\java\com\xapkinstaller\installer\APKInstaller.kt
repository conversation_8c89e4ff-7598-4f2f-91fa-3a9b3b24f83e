package com.xapkinstaller.installer

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInstaller
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import com.xapkinstaller.model.InstallResult
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.io.FileInputStream
import kotlin.coroutines.resume

/**
 * APK installer utility class
 */
class APKInstaller(private val context: Context) {
    
    companion object {
        private const val INSTALL_REQUEST_CODE = 1001
    }
    
    /**
     * Install single APK file
     */
    suspend fun installSingleAPK(apkFile: File): InstallResult {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            installUsingPackageInstaller(listOf(apkFile))
        } else {
            installUsingIntent(apkFile)
        }
    }
    
    /**
     * Install multiple APK files (split APKs)
     */
    suspend fun installMultipleAPKs(apkFiles: List<File>): InstallResult {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            installUsingPackageInstaller(apkFiles)
        } else {
            // For older versions, install main APK only
            val mainApk = apkFiles.firstOrNull()
            if (mainApk != null) {
                installUsingIntent(mainApk)
            } else {
                InstallResult.error(
                    InstallResult.ERROR_APK_INSTALL_FAILED,
                    "No APK files found"
                )
            }
        }
    }
    
    /**
     * Install APK using PackageInstaller (Android 5.0+)
     */
    private suspend fun installUsingPackageInstaller(apkFiles: List<File>): InstallResult {
        return suspendCancellableCoroutine { continuation ->
            try {
                val packageInstaller = context.packageManager.packageInstaller
                val params = PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL)
                
                // Enable multiple APKs for split APK installation
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    params.setInstallLocation(PackageInstaller.SessionParams.INSTALL_LOCATION_AUTO)
                }
                
                val sessionId = packageInstaller.createSession(params)
                val session = packageInstaller.openSession(sessionId)
                
                // Write APK files to session
                apkFiles.forEachIndexed { index, apkFile ->
                    val sessionStream = session.openWrite("apk_$index", 0, apkFile.length())
                    FileInputStream(apkFile).use { input ->
                        input.copyTo(sessionStream)
                    }
                    session.fsync(sessionStream)
                    sessionStream.close()
                }
                
                // Create install intent
                val intent = Intent(context, InstallResultReceiver::class.java)
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    INSTALL_REQUEST_CODE,
                    intent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    } else {
                        PendingIntent.FLAG_UPDATE_CURRENT
                    }
                )
                
                // Set result callback
                InstallResultReceiver.setCallback { result ->
                    continuation.resume(result)
                }
                
                // Commit session
                session.commit(pendingIntent.intentSender)
                session.close()
                
            } catch (e: Exception) {
                continuation.resume(
                    InstallResult.error(
                        InstallResult.ERROR_APK_INSTALL_FAILED,
                        "Failed to install APK: ${e.message}"
                    )
                )
            }
        }
    }
    
    /**
     * Install APK using Intent (Legacy method)
     */
    private suspend fun installUsingIntent(apkFile: File): InstallResult {
        return try {
            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.xapkinstaller.fileprovider",
                    apkFile
                )
            } else {
                Uri.fromFile(apkFile)
            }
            
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "application/vnd.android.package-archive")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
            }
            
            context.startActivity(intent)
            
            // For intent-based installation, we can't get the actual result
            // so we return success assuming user will complete the installation
            InstallResult.success(message = "Installation intent launched")
            
        } catch (e: Exception) {
            InstallResult.error(
                InstallResult.ERROR_APK_INSTALL_FAILED,
                "Failed to launch install intent: ${e.message}"
            )
        }
    }
    
    /**
     * Check if app has install permission
     */
    fun hasInstallPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.packageManager.canRequestPackageInstalls()
        } else {
            true
        }
    }
    
    /**
     * Check if package is already installed
     */
    fun isPackageInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
}
