package com.xapkinstaller

import android.content.Context
import com.xapkinstaller.model.InstallResult
import com.xapkinstaller.model.XAPKInfo
import com.xapkinstaller.parser.XAPKParser
import com.xapkinstaller.utils.DeviceUtils
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockedStatic
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import java.io.File

@RunWith(MockitoJUnitRunner::class)
class XAPKInstallerTest {
    
    @Mock
    private lateinit var mockContext: Context
    
    @Mock
    private lateinit var mockFile: File
    
    private lateinit var xapkParser: XAPKParser
    
    @Before
    fun setUp() {
        xapkParser = XAPKParser(mockContext)
    }
    
    @Test
    fun testIsValidXAPK_withValidFile_returnsTrue() {
        // Given
        `when`(mockFile.exists()).thenReturn(true)
        `when`(mockFile.canRead()).thenReturn(true)
        `when`(mockFile.name).thenReturn("test.xapk")
        
        // When
        val result = xapkParser.isValidXAPK(mockFile)
        
        // Then
        // Note: This test would need actual ZIP file handling to work properly
        // For now, it demonstrates the test structure
        verify(mockFile).exists()
        verify(mockFile).canRead()
        verify(mockFile).name
    }
    
    @Test
    fun testIsValidXAPK_withNonExistentFile_returnsFalse() {
        // Given
        `when`(mockFile.exists()).thenReturn(false)
        
        // When
        val result = xapkParser.isValidXAPK(mockFile)
        
        // Then
        assert(!result)
        verify(mockFile).exists()
    }
    
    @Test
    fun testIsValidXAPK_withNonXAPKExtension_returnsFalse() {
        // Given
        `when`(mockFile.exists()).thenReturn(true)
        `when`(mockFile.canRead()).thenReturn(true)
        `when`(mockFile.name).thenReturn("test.apk")
        
        // When
        val result = xapkParser.isValidXAPK(mockFile)
        
        // Then
        assert(!result)
    }
    
    @Test
    fun testDeviceUtils_supportsSplitAPKInstallation() {
        // This test would need to mock Build.VERSION.SDK_INT
        // For demonstration purposes
        val supports = DeviceUtils.supportsSplitAPKInstallation()
        // The result depends on the actual device running the test
        assert(supports is Boolean)
    }
    
    @Test
    fun testInstallResult_success() {
        // Given
        val packageName = "com.example.test"
        val message = "Installation successful"
        
        // When
        val result = InstallResult.success(packageName, message)
        
        // Then
        assert(result.success)
        assert(result.packageName == packageName)
        assert(result.message == message)
        assert(result.errorCode == 0)
    }
    
    @Test
    fun testInstallResult_error() {
        // Given
        val errorCode = InstallResult.ERROR_INVALID_XAPK
        val message = "Invalid XAPK file"
        val packageName = "com.example.test"
        
        // When
        val result = InstallResult.error(errorCode, message, packageName)
        
        // Then
        assert(!result.success)
        assert(result.errorCode == errorCode)
        assert(result.message == message)
        assert(result.packageName == packageName)
    }
    
    @Test
    fun testXAPKInfo_dataModel() {
        // Given
        val packageName = "com.example.test"
        val name = "Test App"
        val versionCode = 1L
        val versionName = "1.0"
        
        // When
        val xapkInfo = XAPKInfo(
            packageName = packageName,
            name = name,
            versionCode = versionCode,
            versionName = versionName
        )
        
        // Then
        assert(xapkInfo.packageName == packageName)
        assert(xapkInfo.name == name)
        assert(xapkInfo.versionCode == versionCode)
        assert(xapkInfo.versionName == versionName)
        assert(xapkInfo.xapkVersion == 1) // default value
    }
}
