package com.xapkinstaller

import android.content.Context
import com.xapkinstaller.installer.APKInstaller
import com.xapkinstaller.installer.OBBInstaller
import com.xapkinstaller.listener.InstallListener
import com.xapkinstaller.model.InstallProgress
import com.xapkinstaller.model.InstallResult
import com.xapkinstaller.model.InstallStage
import com.xapkinstaller.parser.XAPKParser
import com.xapkinstaller.utils.DeviceUtils
import com.xapkinstaller.utils.PermissionUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Main XAPK Installer SDK class
 * 
 * This class provides the main API for installing XAPK files on Android devices.
 * It handles the complete installation process including:
 * - XAPK file validation and parsing
 * - APK extraction and installation
 * - OBB file handling
 * - Permission management
 * - Device compatibility checks
 * 
 * Usage:
 * ```kotlin
 * val installer = XAPKInstaller(context)
 * installer.install(xapkFile, object : InstallListener {
 *     override fun onInstallStarted(packageName: String) {
 *         // Installation started
 *     }
 *     
 *     override fun onProgressUpdate(progress: InstallProgress) {
 *         // Update progress UI
 *     }
 *     
 *     override fun onInstallCompleted(result: InstallResult) {
 *         // Handle installation result
 *     }
 * })
 * ```
 */
class XAPKInstaller(private val context: Context) {
    
    private val xapkParser = XAPKParser(context)
    private val apkInstaller = APKInstaller(context)
    private val obbInstaller = OBBInstaller(context)
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    /**
     * Install XAPK file
     * 
     * @param xapkFile The XAPK file to install
     * @param listener Installation progress and result listener
     */
    fun install(xapkFile: File, listener: InstallListener) {
        coroutineScope.launch {
            try {
                installInternal(xapkFile, listener)
            } catch (e: Exception) {
                listener.onInstallCompleted(
                    InstallResult.error(
                        InstallResult.ERROR_UNKNOWN,
                        "Unexpected error: ${e.message}"
                    )
                )
            }
        }
    }
    
    /**
     * Internal installation implementation
     */
    private suspend fun installInternal(xapkFile: File, listener: InstallListener) = withContext(Dispatchers.IO) {
        // Step 1: Validate XAPK file
        withContext(Dispatchers.Main) {
            listener.onProgressUpdate(
                InstallProgress(InstallStage.VALIDATING, 0, "Validating XAPK file...")
            )
        }
        
        if (!xapkParser.isValidXAPK(xapkFile)) {
            withContext(Dispatchers.Main) {
                listener.onInstallCompleted(
                    InstallResult.error(
                        InstallResult.ERROR_INVALID_XAPK,
                        "Invalid XAPK file"
                    )
                )
            }
            return@withContext
        }
        
        // Step 2: Extract XAPK
        withContext(Dispatchers.Main) {
            listener.onProgressUpdate(
                InstallProgress(InstallStage.EXTRACTING, 20, "Extracting XAPK file...")
            )
        }
        
        val extractedDir = xapkParser.extractXAPK(xapkFile)
        if (extractedDir == null) {
            withContext(Dispatchers.Main) {
                listener.onInstallCompleted(
                    InstallResult.error(
                        InstallResult.ERROR_EXTRACTION_FAILED,
                        "Failed to extract XAPK file"
                    )
                )
            }
            return@withContext
        }
        
        try {
            // Step 3: Parse manifest
            withContext(Dispatchers.Main) {
                listener.onProgressUpdate(
                    InstallProgress(InstallStage.PARSING_MANIFEST, 40, "Parsing manifest...")
                )
            }
            
            val xapkInfo = xapkParser.parseManifest(extractedDir)
            if (xapkInfo == null) {
                withContext(Dispatchers.Main) {
                    listener.onInstallCompleted(
                        InstallResult.error(
                            InstallResult.ERROR_MANIFEST_PARSE_FAILED,
                            "Failed to parse manifest.json"
                        )
                    )
                }
                return@withContext
            }
            
            // Notify installation started
            withContext(Dispatchers.Main) {
                listener.onInstallStarted(xapkInfo.packageName)
            }
            
            // Step 4: Install APKs
            withContext(Dispatchers.Main) {
                listener.onProgressUpdate(
                    InstallProgress(InstallStage.INSTALLING_APKS, 60, "Installing APK files...")
                )
            }
            
            val apkFiles = xapkParser.getApkFiles(extractedDir, xapkInfo)
            if (apkFiles.isEmpty()) {
                withContext(Dispatchers.Main) {
                    listener.onInstallCompleted(
                        InstallResult.error(
                            InstallResult.ERROR_APK_INSTALL_FAILED,
                            "No APK files found in XAPK"
                        )
                    )
                }
                return@withContext
            }
            
            val apkInstallResult = if (apkFiles.size == 1) {
                apkInstaller.installSingleAPK(apkFiles.first())
            } else {
                apkInstaller.installMultipleAPKs(apkFiles)
            }
            
            if (!apkInstallResult.success) {
                withContext(Dispatchers.Main) {
                    listener.onInstallCompleted(apkInstallResult)
                }
                return@withContext
            }
            
            // Step 5: Install OBB files
            withContext(Dispatchers.Main) {
                listener.onProgressUpdate(
                    InstallProgress(InstallStage.COPYING_OBBS, 80, "Installing OBB files...")
                )
            }
            
            val obbFiles = xapkParser.getObbFiles(extractedDir, xapkInfo)
            val obbInstallResult = obbInstaller.installOBBFiles(obbFiles, xapkInfo)
            
            if (!obbInstallResult.success) {
                withContext(Dispatchers.Main) {
                    listener.onInstallCompleted(obbInstallResult)
                }
                return@withContext
            }
            
            // Step 6: Complete
            withContext(Dispatchers.Main) {
                listener.onProgressUpdate(
                    InstallProgress(InstallStage.COMPLETED, 100, "Installation completed")
                )
                listener.onInstallCompleted(
                    InstallResult.success(
                        packageName = xapkInfo.packageName,
                        message = "XAPK installed successfully"
                    )
                )
            }
            
        } finally {
            // Clean up extracted files
            xapkParser.cleanup(extractedDir)
        }
    }
    
    /**
     * Check if XAPK file is valid
     */
    fun isValidXAPK(xapkFile: File): Boolean {
        return xapkParser.isValidXAPK(xapkFile)
    }
    
    /**
     * Check if all required permissions are granted
     */
    fun hasRequiredPermissions(): Boolean {
        return PermissionUtils.hasAllRequiredPermissions(context)
    }
    
    /**
     * Get permission status message
     */
    fun getPermissionStatus(): String {
        return PermissionUtils.getPermissionStatusMessage(context)
    }
    
    /**
     * Check device compatibility
     */
    fun isDeviceCompatible(): Boolean {
        return DeviceUtils.hasMinimumAndroidVersion()
    }
    
    /**
     * Get device compatibility information
     */
    fun getDeviceInfo(): String {
        return "${DeviceUtils.getDeviceInfo()}\n${DeviceUtils.getAndroidVersionInfo()}\n${DeviceUtils.getROMInfo()}"
    }
    
    /**
     * Get compatibility warning if any
     */
    fun getCompatibilityWarning(): String? {
        return DeviceUtils.getCompatibilityWarning()
    }
}
