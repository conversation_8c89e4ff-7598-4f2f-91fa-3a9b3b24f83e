package com.xapkinstaller.utils

import android.os.Build

/**
 * Device compatibility utility class
 */
object DeviceUtils {
    
    /**
     * Check if device supports split APK installation
     */
    fun supportsSplitAPKInstallation(): Bo<PERSON>an {
        // Split APK installation is supported from Android 5.0 (API 21)
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP
    }
    
    /**
     * Check if device is running a problematic ROM
     */
    fun isProblematicROM(): Bo<PERSON>an {
        return isMIUI() || isFlymeOS() || isFuntouchOS()
    }
    
    /**
     * Check if device is running MIUI (Xiaomi)
     */
    fun isMIUI(): Bo<PERSON>an {
        return !getSystemProperty("ro.miui.ui.version.name").isNullOrEmpty()
    }
    
    /**
     * Check if device is running Flyme OS (Meizu)
     */
    fun isFlymeOS(): Boolean {
        return !getSystemProperty("ro.build.display.id").isNullOrEmpty() &&
                getSystemProperty("ro.build.display.id")?.lowercase()?.contains("flyme") == true
    }
    
    /**
     * Check if device is running Funtouch OS (Vivo)
     */
    fun isFuntouchOS(): <PERSON><PERSON><PERSON> {
        return !getSystemProperty("ro.vivo.os.version").isNullOrEmpty()
    }
    
    /**
     * Get device ROM information
     */
    fun getROMInfo(): String {
        return when {
            isMIUI() -> "MIUI (${getSystemProperty("ro.miui.ui.version.name")})"
            isFlymeOS() -> "Flyme OS"
            isFuntouchOS() -> "Funtouch OS (${getSystemProperty("ro.vivo.os.version")})"
            else -> "Stock Android"
        }
    }
    
    /**
     * Get compatibility warning message for problematic ROMs
     */
    fun getCompatibilityWarning(): String? {
        return when {
            isMIUI() -> "MIUI detected. Please disable MIUI optimization in Developer Options for successful installation."
            isFlymeOS() -> "Flyme OS detected. Split APK installation may not work properly on this ROM."
            isFuntouchOS() -> "Funtouch OS detected. Split APK installation may not work properly on this ROM."
            else -> null
        }
    }
    
    /**
     * Check if device has sufficient Android version for XAPK installation
     */
    fun hasMinimumAndroidVersion(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP // API 21
    }
    
    /**
     * Get Android version information
     */
    fun getAndroidVersionInfo(): String {
        return "Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})"
    }
    
    /**
     * Get device information
     */
    fun getDeviceInfo(): String {
        return "${Build.MANUFACTURER} ${Build.MODEL} (${Build.DEVICE})"
    }
    
    /**
     * Get system property value
     */
    private fun getSystemProperty(key: String): String? {
        return try {
            val process = Runtime.getRuntime().exec("getprop $key")
            process.inputStream.bufferedReader().readLine()?.trim()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if device supports modern package installation methods
     */
    fun supportsModernInstallation(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP
    }
    
    /**
     * Get recommended installation method
     */
    fun getRecommendedInstallationMethod(): String {
        return if (supportsModernInstallation()) {
            "PackageInstaller API"
        } else {
            "Intent-based installation"
        }
    }
}
